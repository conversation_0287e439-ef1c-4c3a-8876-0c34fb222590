﻿-- <PERSON><PERSON><PERSON> dữ liệu các bảng theo thứ tự tránh lỗi khóa ngoại
DELETE FROM "Product_Vendor";
DELETE FROM "Product";
DELETE FROM "Customer";
DELETE FROM "Vendor";
DELETE FROM "Material";
DELETE FROM "SpecialProductTaxRate";
DELETE FROM "ProcessingType";
DELETE FROM "Category";
DELETE FROM "Unit";

-- Reset lại sequence cho các bảng (ID chạy từ 1 trở lại)
ALTER SEQUENCE "Vendor_ID_seq" RESTART WITH 1;
ALTER SEQUENCE "Unit_ID_seq" RESTART WITH 1;
ALTER SEQUENCE "Category_ID_seq" RESTART WITH 1;
ALTER SEQUENCE "ProcessingType_ID_seq" RESTART WITH 1;
ALTER SEQUENCE "Customer_ID_seq" RESTART WITH 1;
ALTER SEQUENCE "SpecialProductTaxRate_ID_seq" RESTART WITH 1;
ALTER SEQUENCE "Material_ID_seq" RESTART WITH 1;
ALTER SEQUENCE "Product_ID_seq" RESTART WITH 1;

-- INSERT dữ liệu mẫu

-- Đơn vị tính (20 dòng)
INSERT INTO "Unit" ("Code", "Name", "Status", "CreatedBy", "UpdatedBy") VALUES
('UNIT01', 'Kilôgam', '0', 1, 1),
('UNIT02', 'Lít', '0', 1, 1),
('UNIT03', 'Thùng', '0', 1, 1),
('UNIT04', 'Hộp', '0', 1, 1),
('UNIT05', 'Gói', '0', 1, 1),
('UNIT06', 'Chai', '0', 1, 1),
('UNIT07', 'Cái', '0', 1, 1),
('UNIT08', 'Bao', '0', 1, 1),
('UNIT09', 'Lon', '0', 1, 1),
('UNIT10', 'Cân', '0', 1, 1),
('UNIT11', 'Túi', '0', 1, 1),
('UNIT12', 'Viên', '0', 1, 1),
('UNIT13', 'Thùng carton', '0', 1, 1),
('UNIT14', 'Kg', '0', 1, 1),
('UNIT15', 'm3', '0', 1, 1),
('UNIT16', 'Cây', '0', 1, 1),
('UNIT17', 'Gói nhỏ', '0', 1, 1),
('UNIT18', 'Hộp lớn', '0', 1, 1),
('UNIT19', 'Lạng', '0', 1, 1),
('UNIT20', 'Thùng nhỏ', '0', 1, 1);

-- Danh mục sản phẩm (20 dòng)
INSERT INTO "Category" ("Code", "Name", "Status", "CreatedBy", "UpdatedBy") VALUES
('CAT01', 'Thực phẩm tươi sống', '0', 1, 1),
('CAT02', 'Thực phẩm chế biến', '0', 1, 1),
('CAT03', 'Đồ uống', '0', 1, 1),
('CAT04', 'Gia vị', '0', 1, 1),
('CAT05', 'Đồ ăn nhanh', '0', 1, 1),
('CAT06', 'Sản phẩm làm bánh', '0', 1, 1),
('CAT07', 'Rau củ quả', '0', 1, 1),
('CAT08', 'Thực phẩm đông lạnh', '0', 1, 1),
('CAT09', 'Hải sản', '0', 1, 1),
('CAT10', 'Sữa và sản phẩm từ sữa', '0', 1, 1),
('CAT11', 'Thịt', '0', 1, 1),
('CAT12', 'Ngũ cốc', '0', 1, 1),
('CAT13', 'Bánh kẹo', '0', 1, 1),
('CAT14', 'Đồ uống có cồn', '0', 1, 1),
('CAT15', 'Nước giải khát', '0', 1, 1),
('CAT16', 'Đồ hộp', '0', 1, 1),
('CAT17', 'Thực phẩm hữu cơ', '0', 1, 1),
('CAT18', 'Đồ ăn nhẹ', '0', 1, 1),
('CAT19', 'Nguyên liệu chế biến', '0', 1, 1),
('CAT20', 'Đồ uống đóng chai', '0', 1, 1);

-- Kiểu chế biến (20 dòng)
INSERT INTO "ProcessingType" ("Code", "Name", "Status", "CreatedBy", "UpdatedBy") VALUES
('PROC01', 'Đông lạnh', '0', 1, 1),
('PROC02', 'Đóng hộp', '0', 1, 1),
('PROC03', 'Sấy khô', '0', 1, 1),
('PROC04', 'Tươi sống', '0', 1, 1),
('PROC05', 'Hấp', '0', 1, 1),
('PROC06', 'Nướng', '0', 1, 1),
('PROC07', 'Luộc', '0', 1, 1),
('PROC08', 'Xông khói', '0', 1, 1),
('PROC09', 'Rán', '0', 1, 1),
('PROC10', 'Kho', '0', 1, 1),
('PROC11', 'Làm lạnh', '0', 1, 1),
('PROC12', 'Đóng gói', '0', 1, 1),
('PROC13', 'Làm mềm', '0', 1, 1),
('PROC14', 'Ủ men', '0', 1, 1),
('PROC15', 'Cắt nhỏ', '0', 1, 1),
('PROC16', 'Trộn', '0', 1, 1),
('PROC17', 'Ép', '0', 1, 1),
('PROC18', 'Chưng cất', '0', 1, 1),
('PROC19', 'Làm đông', '0', 1, 1),
('PROC20', 'Ủ lạnh', '0', 1, 1);

-- Thuế sản phẩm đặc biệt (20 dòng)
INSERT INTO "SpecialProductTaxRate" ("Code", "Name", "Status", "CreatedBy", "UpdatedBy") VALUES
('TAX01', 'Thuế thực phẩm nhập khẩu', '0', 1, 1),
('TAX02', 'Thuế vệ sinh an toàn', '0', 1, 1),
('TAX03', 'Thuế hàng xa xỉ', '0', 1, 1),
('TAX04', 'Thuế bảo vệ sức khỏe', '0', 1, 1),
('TAX05', 'Thuế rượu bia', '0', 1, 1),
('TAX06', 'Thuế thuốc lá', '0', 1, 1),
('TAX07', 'Thuế môi trường', '0', 1, 1),
('TAX08', 'Thuế dầu mỏ', '0', 1, 1),
('TAX09', 'Thuế hàng điện tử', '0', 1, 1),
('TAX10', 'Thuế nhập khẩu đặc biệt', '0', 1, 1),
('TAX11', 'Thuế bán hàng', '0', 1, 1),
('TAX12', 'Thuế tiêu thụ đặc biệt', '0', 1, 1),
('TAX13', 'Thuế nhập khẩu ưu đãi', '0', 1, 1),
('TAX14', 'Thuế chống bán phá giá', '0', 1, 1),
('TAX15', 'Thuế chống trợ cấp', '0', 1, 1),
('TAX16', 'Thuế môi trường phát thải', '0', 1, 1),
('TAX17', 'Thuế khai thác tài nguyên', '0', 1, 1),
('TAX18', 'Thuế đóng góp xã hội', '0', 1, 1),
('TAX19', 'Thuế phí hải quan', '0', 1, 1),
('TAX20', 'Thuế bảo hiểm', '0', 1, 1);

-- Chất liệu (20 dòng)
INSERT INTO "Material" ("Code", "Name", "Status", "CreatedBy", "UpdatedBy") VALUES
('MAT01', 'Thịt heo', '0', 1, 1),
('MAT02', 'Cá hồi', '0', 1, 1),
('MAT03', 'Gạo', '0', 1, 1),
('MAT04', 'Muối', '0', 1, 1),
('MAT05', 'Đường', '0', 1, 1),
('MAT06', 'Bơ', '0', 1, 1),
('MAT07', 'Sữa', '0', 1, 1),
('MAT08', 'Bột mì', '0', 1, 1),
('MAT09', 'Dầu ăn', '0', 1, 1),
('MAT10', 'Tiêu', '0', 1, 1),
('MAT11', 'Ớt', '0', 1, 1),
('MAT12', 'Hành', '0', 1, 1),
('MAT13', 'Tỏi', '0', 1, 1),
('MAT14', 'Cà rốt', '0', 1, 1),
('MAT15', 'Khoai tây', '0', 1, 1),
('MAT16', 'Bắp cải', '0', 1, 1),
('MAT17', 'Cải thìa', '0', 1, 1),
('MAT18', 'Dưa chuột', '0', 1, 1),
('MAT19', 'Táo', '0', 1, 1),
('MAT20', 'Chuối', '0', 1, 1);

-- Nhà cung cấp (20 dòng)
INSERT INTO "Vendor" ("Code", "Name", "Status", "CreatedBy", "UpdatedBy") VALUES
('VEND01', 'Công ty Thực phẩm ABC', '0', 1, 1),
('VEND02', 'Siêu thị XYZ', '0', 1, 1),
('VEND03', 'Cửa hàng Tiện lợi 123', '0', 1, 1),
('VEND04', 'Công ty Nông sản Miền Tây', '0', 1, 1),
('VEND05', 'Công ty Xuất nhập khẩu ABC', '0', 1, 1),
('VEND06', 'Nhà phân phối XYZ', '0', 1, 1),
('VEND07', 'Cửa hàng Rau sạch', '0', 1, 1),
('VEND08', 'Công ty Thực phẩm X', '0', 1, 1),
('VEND09', 'Siêu thị An Bình', '0', 1, 1),
('VEND10', 'Cửa hàng Tiện lợi 456', '0', 1, 1),
('VEND11', 'Nhà cung cấp thực phẩm Y', '0', 1, 1),
('VEND12', 'Công ty thực phẩm Z', '0', 1, 1),
('VEND13', 'Đại lý phân phối A', '0', 1, 1),
('VEND14', 'Nhà cung cấp B', '0', 1, 1),
('VEND15', 'Công ty Nông sản Đông Nam', '0', 1, 1),
('VEND16', 'Siêu thị Hòa Bình', '0', 1, 1),
('VEND17', 'Cửa hàng thực phẩm C', '0', 1, 1),
('VEND18', 'Nhà phân phối D', '0', 1, 1),
('VEND19', 'Công ty Thực phẩm E', '0', 1, 1),
('VEND20', 'Cửa hàng G', '0', 1, 1);

-- Khách hàng (20 dòng)
INSERT INTO "Customer" ("Code", "Name", "Status", "CreatedBy", "UpdatedBy") VALUES
('CUS01', 'Nguyễn Văn A', '0', 1, 1),
('CUS02', 'Trần Thị B', '0', 1, 1),
('CUS03', 'Công ty TNHH Thực phẩm An Toàn', '0', 1, 1),
('CUS04', 'Lê Văn C', '0', 1, 1),
('CUS05', 'Phạm Thị D', '0', 1, 1),
('CUS06', 'Công ty XYZ', '0', 1, 1),
('CUS07', 'Nguyễn Thị E', '0', 1, 1),
('CUS08', 'Trần Văn F', '0', 1, 1),
('CUS09', 'Công ty GHI', '0', 1, 1),
('CUS10', 'Lê Thị H', '0', 1, 1),
('CUS11', 'Nguyễn Văn I', '0', 1, 1),
('CUS12', 'Trần Thị J', '0', 1, 1),
('CUS13', 'Công ty KLM', '0', 1, 1),
('CUS14', 'Lê Văn M', '0', 1, 1),
('CUS15', 'Phạm Thị N', '0', 1, 1),
('CUS16', 'Công ty NOP', '0', 1, 1),
('CUS17', 'Nguyễn Thị O', '0', 1, 1),
('CUS18', 'Trần Văn P', '0', 1, 1),
('CUS19', 'Công ty QRS', '0', 1, 1),
('CUS20', 'Lê Thị T', '0', 1, 1);

-- Sản phẩm (20 dòng)
INSERT INTO "Product" (
    "Code", "Name", "Unit_ID", "Category_ID", "ProcessingType_ID",
    "TaxRate", "Material_ID", "CompanyTaxRate", "ConsumerTaxRate",
    "SpecialProductTaxRate_ID", "Status", "CreatedBy", "UpdatedBy"
) VALUES
('PROD01', 'Thịt heo đông lạnh', 1, 1, 1, 8.00, 1, 5.00, 7.00, 1, '0', 1, 1),
('PROD02', 'Cá hồi đóng hộp', 2, 2, 2, 12.00, 2, 6.00, 9.00, 2, '0', 1, 1),
('PROD03', 'Gạo sấy khô', 1, 1, 3, 5.00, 3, 3.00, 4.00, 3, '0', 1, 1),
('PROD04', 'Muối tươi', 4, 4, 4, 3.00, 4, 2.00, 2.50, 4, '0', 1, 1),
('PROD05', 'Đường kính', 5, 1, 5, 4.00, 5, 3.00, 3.50, 5, '0', 1, 1),
('PROD06', 'Bơ nhạt', 6, 6, 6, 6.00, 6, 4.00, 5.00, 6, '0', 1, 1),
('PROD07', 'Sữa tươi', 7, 10, 7, 10.00, 7, 6.00, 8.00, 7, '0', 1, 1),
('PROD08', 'Bột mì đa dụng', 8, 6, 8, 7.00, 8, 5.00, 6.00, 8, '0', 1, 1),
('PROD09', 'Dầu ăn thực vật', 9, 1, 9, 9.00, 9, 7.00, 8.50, 9, '0', 1, 1),
('PROD10', 'Tiêu đen', 10, 4, 10, 3.00, 10, 2.50, 3.50, 10, '0', 1, 1),
('PROD11', 'Ớt bột', 11, 4, 11, 4.00, 11, 3.50, 4.00, 11, '0', 1, 1),
('PROD12', 'Hành tây', 12, 7, 12, 2.00, 12, 2.00, 2.50, 12, '0', 1, 1),
('PROD13', 'Tỏi khô', 13, 4, 13, 3.00, 13, 2.50, 3.00, 13, '0', 1, 1),
('PROD14', 'Cà rốt tươi', 14, 7, 14, 2.50, 14, 2.00, 2.50, 14, '0', 1, 1),
('PROD15', 'Khoai tây', 15, 7, 15, 3.00, 15, 2.50, 3.00, 15, '0', 1, 1),
('PROD16', 'Bắp cải', 16, 7, 16, 2.00, 16, 1.50, 2.00, 16, '0', 1, 1),
('PROD17', 'Cải thìa', 17, 7, 17, 2.00, 17, 1.50, 2.00, 17, '0', 1, 1),
('PROD18', 'Dưa chuột', 18, 7, 18, 2.00, 18, 1.50, 2.00, 18, '0', 1, 1),
('PROD19', 'Táo đỏ', 19, 19, 19, 4.00, 19, 3.00, 3.50, 19, '0', 1, 1),
('PROD20', 'Chuối tiêu', 20, 20, 20, 3.00, 20, 2.50, 3.00, 20, '0', 1, 1);

-- Bảng liên kết Sản phẩm - Nhà cung cấp (20 dòng)
INSERT INTO "Product_Vendor" ("Vendor_ID", "Product_ID", "Price", "UnitPrice", "Priority") VALUES
(1, 1, 150000, 150000, 1),
(2, 2, 120000, 120000, 1),
(3, 3, 100000, 100000, 1),
(4, 4, 90000, 90000, 1),
(5, 5, 110000, 110000, 1),
(6, 6, 130000, 130000, 1),
(7, 7, 140000, 140000, 1),
(8, 8, 160000, 160000, 1),
(9, 9, 170000, 170000, 1),
(10, 10, 90000, 90000, 1),
(11, 11, 85000, 85000, 1),
(12, 12, 95000, 95000, 1),
(13, 13, 105000, 105000, 1),
(14, 14, 97000, 97000, 1),
(15, 15, 88000, 88000, 1),
(16, 16, 93000, 93000, 1),
(17, 17, 102000, 102000, 1),
(18, 18, 94000, 94000, 1),
(19, 19, 99000, 99000, 1),
(20, 20, 101000, 101000, 1);
